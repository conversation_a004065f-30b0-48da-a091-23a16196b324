package com.bukuwarung.contact.ui

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.bukuwarung.activities.phonebook.ContactRepository
import com.bukuwarung.activities.phonebook.model.Contact
import com.bukuwarung.activities.phonebook.model.RowHolder
import com.bukuwarung.activities.phonebook.model.RowHolder.ContactRowHolder
import com.bukuwarung.activities.phonebook.model.RowHolder.CustomerSeparatorHolder
import com.bukuwarung.activities.phonebook.model.RowHolder.FavoriteCustomerSeparatorHolder
import com.bukuwarung.activities.phonebook.model.RowHolder.LoadMoreRecommendationSeparatorHolder
import com.bukuwarung.activities.phonebook.model.RowHolder.RecommendationCustomerSeparatorHolder
import com.bukuwarung.activities.phonebook.model.RowHolder.SeparatorHolder
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.contact.usecases.DeviceAndUserAddedContacts
import com.bukuwarung.contact.usecases.DeviceContacts
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.domain.payments.FinproUseCase
import com.bukuwarung.favoritecustomer.FavoriteCustomerUseCase
import com.bukuwarung.payments.constants.PpobConst
import com.bukuwarung.payments.data.model.ppob.ProfilesItem
import com.bukuwarung.session.SessionManager
import com.bukuwarung.session.User
import com.bukuwarung.utils.ListUtils
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.isTrue
import com.bukuwarung.utils.orNil
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

@HiltViewModel
class ContactSearchViewModel @Inject constructor(private val deviceAndUserAddedContacts: DeviceAndUserAddedContacts,
                                                 private val deviceContactUseCase: DeviceContacts,
                                                 private val favoriteCustomerUseCase: FavoriteCustomerUseCase,
                                                 private val finproUseCase: FinproUseCase,
                                                 private val backgroundDispatcher: CoroutineDispatcher) : ViewModel() {
    private var contactList: ArrayList<ContactListDto> = arrayListOf()
    private var favoriteContacts: List<Contact> = listOf()
    private var recommendationContacts: List<ContactListDto> = listOf()
    private var userContacts: List<Contact> = listOf()
    private var deviceContacts: List<Contact> = listOf()
    private var contactRowHolderList: ArrayList<RowHolder> = arrayListOf()

    private val contactsMutableObserver = MutableLiveData<ContactEvent>()
    val contactsObserver: LiveData<ContactEvent> = contactsMutableObserver
    private val recommendationPageCount = PpobConst.RECOMMENDATION_CONTACTS_COUNT
    private var recommendationsPageNumber = 0

    private val addContactMutableObserver: MutableLiveData<AddCustomerEvent> = MutableLiveData()
    val addContactObserver: LiveData<AddCustomerEvent> = addContactMutableObserver

    private val inputContactNameMutableObserver: MutableLiveData<OnContactNameInput> = MutableLiveData()
    val inputContactNameObserver: LiveData<OnContactNameInput> = inputContactNameMutableObserver

    private val businessId: String = User.getBusinessId()
    private var query: String = "&"

    private val favoriteCustomerConfig = RemoteConfigUtils.FavoriteCustomer
    private val isFavoriteCustomerEnabled = favoriteCustomerConfig.isEnabled()
    private val listFavoriteCustomersLimit = favoriteCustomerConfig.getListFavoriteCustomerLimit().toInt()
    private val recentCustomersLimit = favoriteCustomerConfig.getRecentCustomerLimit().toInt()
    private var showFavoriteContact: Boolean = false
    private var searchUseCase = CustomerSearchUseCase.ACCOUNTING
    private var orderId = ""

    fun init(useCase: CustomerSearchUseCase,orderId: String = "") {
        searchUseCase = useCase
        if(this.orderId.isEmpty())
            this.orderId = orderId

    }

    fun onSearchTextChange(queryStr: String, refreshCache: Boolean = false) {
        recommendationsPageNumber = 0//when user starts to type the query, we should be change the value of page number to 0
        recommendationContacts = emptyList() // to fetch new results assigning it to empty list
        if (queryStr.trim() == query && !refreshCache) {
            return
        }
//        showLoader(true)
        query = queryStr.trim()
        if (query.isNotEmpty()) {
            addContactMutableObserver.value = AddCustomerEvent.ShowCustomerWidget("\"" + query + "\"")
            inputContactNameMutableObserver.value = OnContactNameInput.UpdateInputName(query)
        } else {
            addContactMutableObserver.value = AddCustomerEvent.HideCustomerWidget
        }
        viewModelScope.launch {
            contactList.clear()
            contactRowHolderList.clear()

            if (showFavoriteContact && isFavoriteCustomerEnabled) {
                mostTransactingContact(query)
                if (favoriteContacts.isNotEmpty()) {
                    contactList.add(ContactListDto(Contact("", "", "", ContactRepository.DUMMY_FAVORITE_TITLE_SEPARATOR), AnalyticsConst.PHONEBOOK_FAV_CUSTOMER_SECTION))
                }
                contactList.addAll(favoriteContacts.map { ContactListDto(it, AnalyticsConst.PHONEBOOK_FAV_CUSTOMER_SECTION) })
                if (contactList.isNotEmpty()) {
                    contactList = contactList.distinct() as ArrayList<ContactListDto>
                }
            }
            if(orderId.isNotBlank())//api call to fetch the recommendations
                fetchRecommendations(refreshCache)
            else {
                userAddedContacts(query, refreshCache)
                addPhoneContacts(refreshCache)
            }
        }
    }

    private fun showLoader(flag: Boolean) {
            contactsMutableObserver.value = ContactEvent.ShowLoaderOnSearchFragment(flag)
            contactsMutableObserver.value = ContactEvent.ShowLoaderOnSearchResultsFragment(flag)
    }

    private fun addPhoneContacts(refreshCache: Boolean) = viewModelScope.launch {
        if (userContacts.isNotEmpty()) {
            contactList.add(ContactListDto(Contact("", "", "", ContactRepository.DUMMY_TITLE_SEPARATOR), AnalyticsConst.PHONEBOOK_EXISTING_RECORD_USER_SECTION))
        }
        contactList.addAll(userContacts.map { ContactListDto(it, AnalyticsConst.PHONEBOOK_EXISTING_RECORD_USER_SECTION) })
        if (contactList.isNotEmpty()) {
            contactList = contactList.distinct() as ArrayList<ContactListDto>
        }
        deviceContacts(query, refreshCache)
        if (deviceContacts.isNotEmpty()) {
            contactList.add(ContactListDto(Contact("", "", "", ContactRepository.DUMMY_SEPARATOR), AnalyticsConst.PHONEBOOK_NO_RCRDS__USER_SECTION))
        }
        contactList.addAll(deviceContacts.map { ContactListDto(it, AnalyticsConst.PHONEBOOK_NO_RCRDS__USER_SECTION) })
        if (contactList.isNotEmpty()) {
            contactList = contactList.distinct() as ArrayList<ContactListDto>
        }
        showLoader(false)
        contactsMutableObserver.value = ContactEvent.DisplayContacts(contactsToViewHolderList(contactRowHolderList, contactList), refreshCache, query)
    }

    private suspend fun mostTransactingContact(query: String) =
            withContext(backgroundDispatcher) {
                favoriteContacts = favoriteCustomerUseCase.getFavoriteContacts(listFavoriteCustomersLimit)
                        .filter { it.name?.contains(query, true).isTrue || it.mobile?.contains(query, true).isTrue }
            }

    fun fetchRecommendations(refreshCache: Boolean, incrementPageNumber: Boolean = false) =
        viewModelScope.launch {
            if (incrementPageNumber) {
                contactList.clear()
                contactRowHolderList.clear()
                recommendationsPageNumber += 1
                showLoader(true)
            }
            withContext(Dispatchers.IO) {
                when (val response = finproUseCase.getRecommendations(
                    SessionManager.getInstance().businessId,
                    orderId,
                    query,
                    recommendationsPageNumber,
                    recommendationPageCount
                )) {
                    is ApiSuccessResponse -> {
                        //logic to add the list of recommendation contacts and followed with adding phone contacts
                        recommendationContacts = convertListOfProfileItemsToContact(
                            response.body.profiles ?: emptyList()
                        )
                        if (recommendationContacts.isNotEmpty()) {
                            contactList.add(
                                ContactListDto(
                                    Contact(
                                        "",
                                        "",
                                        null,
                                        ContactRepository.DUMMY_RECOMMENDATION_TITLE_SEPARATOR
                                    ), AnalyticsConst.PHONEBOOK_RECOMMENDATION_CUSTOMER_SECTION
                                )
                            )
                        }
                        contactList.addAll(recommendationContacts)
                        if (contactList.isNotEmpty()) {
                            contactList = contactList.distinct() as ArrayList<ContactListDto>
                        }
                        //checking whether to show the load more button or not by checking if any more elements are present to be loaded, if true then adding it.
                        //so this condition can be checked by subtracting total recommendations shown from total recommendations present
                        //so total recommendations present = response.body.metadata?.totalCount?:0
                        //total recommendations already shown on screen = ((recommendationsPageNumber)*recommendationPageCount) - (response.body.profiles?.size?:0)
                        //if the resultant value > 0, means there are more contacts which can be loaded.
                        val flag: Boolean =
                            response.body.metadata?.totalCount.orNil - (recommendationsPageNumber * recommendationPageCount) - response.body.profiles?.size.orNil > 0
                        if (flag) {
                            contactList.add(
                                ContactListDto(
                                    Contact(
                                        "",
                                        "",
                                        "",
                                        ContactRepository.DUMMY_RECOMMENDATION_BUTTON_SEPARATOR
                                    ), AnalyticsConst.LOAD_MORE_RECOMMENDATION_CUSTOMER_SECTION
                                )
                            )
                        }
                        //after showing recommendations from backend, showing contacts from phone book.
                        addPhoneContacts(refreshCache)
                    }
                    else -> {
                        //if backend fails to send recommendation contacts, then we show only the phone contacts.
                        addPhoneContacts(refreshCache)
                    }
                }
            }
        }

    private fun convertListOfProfileItemsToContact(profileList: List<ProfilesItem>): List<ContactListDto> {
        val contactList: ArrayList<ContactListDto> = ArrayList(recommendationContacts)
        contactList.addAll(profileList.map {
            ContactListDto(
                Contact(
                    it.alias,
                    "",// we are using : to highlight additional count with different color compared to the favourite text on Contact Adapter file. example
                    null,
                    ""
                ),
                AnalyticsConst.PHONEBOOK_RECOMMENDATION_CUSTOMER_SECTION,
                it.favouritesText.orEmpty(),
                it.additionalCount.orEmpty()
            )
        })
        return contactList.toList()
    }

    private suspend fun userAddedContacts(query: String, refreshCache: Boolean) =
            withContext(backgroundDispatcher) {
                if (businessId.isEmpty()) {
                    return@withContext
                }
                userContacts = deviceAndUserAddedContacts.getUserAddedContact(
                    businessId, query, refreshCache,
                    if (showFavoriteContact && isFavoriteCustomerEnabled) recentCustomersLimit else PaymentConst.CONTACTS_LIMIT,
                    searchUseCase
                )
            }

    private suspend fun deviceContacts(query: String, refreshCache: Boolean) =
            withContext(backgroundDispatcher) {
                if (businessId.isEmpty()) {
                    return@withContext
                }
                deviceContacts = deviceAndUserAddedContacts.getContactsExcludeAlreadyAdded(
                    businessId, query, refreshCache, useCase = searchUseCase, limit = PaymentConst.CONTACTS_LIMIT
                )
            }

    fun onSearchQueryChangeForReferral(queryStr: String) = viewModelScope.launch {
        query = queryStr.trim()
        val filteredContact = deviceContacts.filter { it.name.contains(query, true) }
        contactsMutableObserver.value = ContactEvent.DeviceContactsLoaded(filteredContact)
    }

    fun loadDeviceContactOnly() = viewModelScope.launch {
        withContext(backgroundDispatcher) {
            deviceContacts = deviceContactUseCase.getDeviceContacts().distinctBy { it.mobile }
            viewModelScope.launch {
                contactsMutableObserver.value = ContactEvent.DeviceContactsLoaded(deviceContacts)
            }
        }
    }

    private fun contactsToViewHolderList(resultList: ArrayList<RowHolder>, contactList: ArrayList<ContactListDto>): ArrayList<RowHolder> {
        if (!ListUtils.isEmpty(contactList)) {
            val size = contactList.size
            for (i in 0 until size) {
                val contact = contactList[i].contact
                when {
                    contact.customerId != null && contact.customerId == ContactRepository.DUMMY_SEPARATOR -> {
                        resultList.add(SeparatorHolder())
                    }
                    contact.customerId != null && contact.customerId == ContactRepository.DUMMY_TITLE_SEPARATOR -> {
                        resultList.add(CustomerSeparatorHolder())
                    }
                    contact.customerId != null && contact.customerId == ContactRepository.DUMMY_FAVORITE_TITLE_SEPARATOR -> {
                        resultList.add(FavoriteCustomerSeparatorHolder())
                    }
                    contact.customerId != null && contact.customerId == ContactRepository.DUMMY_RECOMMENDATION_TITLE_SEPARATOR -> {
                        resultList.add(RecommendationCustomerSeparatorHolder())
                    }
                    contact.customerId != null && contact.customerId == ContactRepository.DUMMY_RECOMMENDATION_BUTTON_SEPARATOR -> {
                        resultList.add(LoadMoreRecommendationSeparatorHolder())
                    }
                    else -> {
                        val contactSource = contactList[i].source
                        resultList.add(ContactRowHolder(contact, contactSource, contactList[i].favouriteText, contactList[i].favouriteCount))
                    }
                }
            }
        }
        return resultList
    }

    fun onContactSelected(pos: Int, contactSource: String) {
        val holder = contactRowHolderList[pos]
        when (holder) {
            is ContactRowHolder -> {
                contactsMutableObserver.value = ContactEvent.UpdateSelectedCustomer(holder.contact, contactSource)
            }
        }
    }

    fun addNewContact() {
        contactsMutableObserver.value = ContactEvent.UpdateSelectedCustomer(Contact(query, "", "", ""), AnalyticsConst.CUSTOMER_CREATE_MANUAL)
    }

    fun contactPermissionAllowed() {
        onSearchTextChange(query, true)
    }

    fun setShowFavoriteContacts(show: Boolean) {
        showFavoriteContact = show
    }

    sealed class ContactEvent {
        data class ShowLoaderOnSearchFragment(val showLoader: Boolean) : ContactEvent()
        data class ShowLoaderOnSearchResultsFragment(val showLoader: Boolean) : ContactEvent()
        data class DisplayContacts(val contactRowHolder: List<RowHolder>, val refreshCache: Boolean, val query: String) : ContactEvent()
        data class UpdateSelectedCustomer(val contact: Contact, val contactSource: String) : ContactEvent()
        data class DeviceContactsLoaded(val contacts: List<Contact>) : ContactEvent()
    }

    sealed class AddCustomerEvent {
        object HideCustomerWidget : AddCustomerEvent()
        data class ShowCustomerWidget(val customerName: String) : AddCustomerEvent()
    }

    sealed class OnContactNameInput {
        data class UpdateInputName(val inputName: String) : OnContactNameInput()

    }
}

data class ContactListDto(
        val contact: Contact,
        val source: String, // used for analitycs
        val favouriteText: String = "", //used in case of favourite recommendation contacts
        val favouriteCount: String = "" //used in case of favourite recommendation contacts
)