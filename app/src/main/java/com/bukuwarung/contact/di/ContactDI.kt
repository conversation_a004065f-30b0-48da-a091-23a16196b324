package com.bukuwarung.contact.di

import android.content.Context
import com.bukuwarung.contact.ContactLoader
import com.bukuwarung.contact.ContactManager
import com.bukuwarung.contact.ContactViewModelFactory
import com.bukuwarung.contact.DeviceContactRepo
import com.bukuwarung.contact.DeviceContactRepository
import com.bukuwarung.contact.UserContactDataStore
import com.bukuwarung.contact.UserContactRepo
import com.bukuwarung.contact.UserContactRepository
import com.bukuwarung.contact.UserContactStore
import com.bukuwarung.contact.usecases.DeviceAndUserAddedContacts
import com.bukuwarung.contact.usecases.DeviceContacts
import com.bukuwarung.contact.usecases.UserContacts
import com.bukuwarung.database.AppDatabase
import com.bukuwarung.database.repository.TransactionRepository
import com.bukuwarung.domain.payments.FinproUseCase
import com.bukuwarung.favoritecustomer.FavoriteCustomerUseCase
import com.bukuwarung.payments.data.repository.BankAccountLocalRepository
import com.bukuwarung.session.SessionManager
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
class ContactDI {

    @Provides
    fun provideContactManager(context: Context): ContactManager = ContactLoader(context)

    @Provides
    fun provideDeviceContactRepository(contactManager: ContactManager): DeviceContactRepository =
            DeviceContactRepo(contactManager)

    @Provides
    fun provideContactDataStore(appDatabase: AppDatabase): UserContactStore =
            UserContactDataStore(appDatabase.customerDao())

    @Provides
    fun provideUserContactRepository(userContactStore: UserContactStore): UserContactRepository =
            UserContactRepo(userContactStore)

    @Provides
    fun provideDeviceContacts(deviceContactRepository: DeviceContactRepository) =
            DeviceContacts(deviceContactRepository)

    @Provides
    fun provideUserContacts(
        userContactRepository: UserContactRepository,
        bankAccountLocalRepository: BankAccountLocalRepository
    ) = UserContacts(userContactRepository, bankAccountLocalRepository)

    @Provides
    fun provideDeviceAndUserAddedContacts(deviceContacts: DeviceContacts, userContacts: UserContacts) =
            DeviceAndUserAddedContacts(deviceContacts, userContacts)

    @Provides
    fun provideFavoriteCustomersUseCase(transactionRepository: TransactionRepository, sessionManager: SessionManager) = FavoriteCustomerUseCase(transactionRepository, sessionManager)

    @Provides
    fun providesContactViewModelFactory(
        deviceAndUserAddedContacts: DeviceAndUserAddedContacts,
        deviceContactUseCase: DeviceContacts,
        favoriteCustomerUseCase: FavoriteCustomerUseCase,
        finproUseCase: FinproUseCase
    ) =
        ContactViewModelFactory(
            deviceAndUserAddedContacts,
            deviceContactUseCase,
            favoriteCustomerUseCase,
            finproUseCase
        )
}
