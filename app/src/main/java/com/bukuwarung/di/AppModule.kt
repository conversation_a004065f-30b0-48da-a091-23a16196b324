package com.bukuwarung.di

import android.content.Context
import com.bukuwarung.Application
import com.bukuwarung.activities.profile.deeplink.handler.EditUserProfileSignalHandler
import com.bukuwarung.activities.profile.deeplink.handler.NgBusinessProfileSignalHandler
import com.bukuwarung.database.repository.InventoryRepository
import com.bukuwarung.deeplink.handler.EditStockSignalHandler
import com.bukuwarung.deeplink.handler.LegacySignalHandler
import com.bukuwarung.deeplink.handler.MainActivitySignalHandler
import com.bukuwarung.deeplink.handler.SelectCategorySignalHandler
import com.bukuwarung.deeplink.handler.WebViewSignalHandler
import com.bukuwarung.neuro.api.SignalHandler
import com.bukuwarung.payments.deeplink.handler.OrderHistorySignalHandler
import com.bukuwarung.payments.deeplink.handler.PaymentCheckoutSignalHandler
import com.bukuwarung.payments.deeplink.handler.PaymentHistoryDetailsSignalHandler
import com.bukuwarung.payments.deeplink.handler.QrisSignalHandler
import com.bukuwarung.payments.deeplink.handler.SaldoSignalHandler
import com.bukuwarung.payments.ppob.deeplink.handler.PpobOrderFormSignalHandler
import com.bukuwarung.payments.ppob.deeplink.handler.PpobProductDetailsSignalHandler
import com.bukuwarung.payments.ppob.deeplink.handler.PpobProductSignalHandler
import com.google.firebase.firestore.FirebaseFirestore
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import dagger.multibindings.IntoSet
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
class AppModule {
    @Provides
    fun provideApplication(@ApplicationContext context: Context): Application {
        return context as Application
    }

    @Provides
    @Singleton
    @AppScope
    fun provideAppCoroutineScope(): CoroutineScope {
        return CoroutineScope(context = SupervisorJob() + Dispatchers.Main.immediate)
    }

    @Singleton
    @Provides
    fun provideContext(application: Application): Context {
        return application
    }

    @Provides
    fun provideFireStoreInstance(): FirebaseFirestore {
        return FirebaseFirestore.getInstance()
    }

    @Provides
    @Singleton
    fun provideInventoryRepository(@ApplicationContext context: Context): InventoryRepository {
        return InventoryRepository.getInstance(context)
    }

    @Provides
    @IntoSet
    fun provideLegacySignalHandler(handler: LegacySignalHandler): SignalHandler = handler

    @Provides
    @IntoSet
    fun provideWebViewSignalHandler(handler: WebViewSignalHandler): SignalHandler = handler

    @Provides
    @IntoSet
    fun provideMainActivitySignalHandler(handler: MainActivitySignalHandler): SignalHandler = handler

    @Provides
    @IntoSet
    fun providePaymentSignalHandler(handler: OrderHistorySignalHandler): SignalHandler = handler

    @Provides
    @IntoSet
    fun providePaymentCheckoutSignalHandler(handler: PaymentCheckoutSignalHandler): SignalHandler = handler

    @Provides
    @IntoSet
    fun provideQrisSignalHandler(handler: QrisSignalHandler): SignalHandler = handler

    @Provides
    @IntoSet
    fun provideSaldoSignalHandler(handler: SaldoSignalHandler): SignalHandler = handler

    @Provides
    @IntoSet
    fun provideSelectCategorySignalHandler(handler: SelectCategorySignalHandler): SignalHandler = handler

    @Provides
    @IntoSet
    fun provideEditStockSignalHandler(handler: EditStockSignalHandler): SignalHandler = handler

    @Provides
    @IntoSet
    fun providePpobProductSignalHandler(handler: PpobProductSignalHandler): SignalHandler = handler

    @Provides
    @IntoSet
    fun providePpobProductDetailsSignalHandler(handler: PpobProductDetailsSignalHandler): SignalHandler = handler

    @Provides
    @IntoSet
    fun providePpobOrderFormSignalHandler(handler: PpobOrderFormSignalHandler): SignalHandler = handler

    @Provides
    @IntoSet
    fun provideEditUserProfileSignalHandler(handler: EditUserProfileSignalHandler): SignalHandler = handler

    @Provides
    @IntoSet
    fun provideNgBusinessProfileSignalHandler(handler: NgBusinessProfileSignalHandler): SignalHandler = handler

    @Provides
    @IntoSet
    fun providePaymentHistoryDetailsSignalHandler(handler: PaymentHistoryDetailsSignalHandler): SignalHandler = handler
}
