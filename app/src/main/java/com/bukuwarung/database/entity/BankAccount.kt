package com.bukuwarung.database.entity

import android.os.Parcelable
import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Ignore
import androidx.room.PrimaryKey
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.payments.constants.PpobConst
import com.bukuwarung.utils.isTrue
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import org.jetbrains.annotations.NotNull

@Parcelize
@Entity(tableName = "bank_account")
data class BankAccount(
        @PrimaryKey
        @ColumnInfo(name = "bank_account_id")
        @SerializedName("bank_account_id")
        var bankAccountId: String = "",

        @ColumnInfo(name = "account_id")
        @SerializedName("account_id")
        var accountId: String? = null,

        @ColumnInfo(name = "bank_code")
        @SerializedName("bank_code")
        var bankCode: String? = null,

        @ColumnInfo(name = "bank_name")
        @SerializedName("bank_name")
        var bankName: String? = null,

        @ColumnInfo(name = "account_number")
        @SerializedName("account_number")
        var accountNumber: String? = null,

        @ColumnInfo(name = "customer_id")
        @SerializedName("customer_id")
        var customerId: String? = "",

        @ColumnInfo(name = "account_holder_name")
        @SerializedName("account_holder_name")
        var accountHolderName: String? = null,

        @NotNull
        @ColumnInfo(name = "is_selected")
        var isSelected: Int = 0,

        @ColumnInfo(name = "flag")
        @SerializedName("flag")
        var flag: String? = null,

        @ColumnInfo(name = "message")
        @SerializedName("message")
        var message: String? = null,

        @ColumnInfo(name = "is_qris_bank")
        @SerializedName("is_qris_bank")
        var isQrisBank: Boolean? = false,

        @ColumnInfo(name = "matching_status")
        @SerializedName("matching_status")
        var matchingStatus: Boolean? = null,

        @ColumnInfo(name = "is_disabled")
        @SerializedName("is_disabled")
        var isDisabled: Boolean? = null,

        @ColumnInfo(name = "account_relation")
        @SerializedName("account_relation")
        var accountRelation: PaymentConst.BankAccountOwner? = null
): AppEntity(), Parcelable {

    @Ignore
    @SerializedName("status")
    var status: BankAccountStatus? = null

    @Ignore
    @SerializedName("proposed_qris_bank")
    var proposedQrisBank: Boolean? = false

    @Ignore
    @SerializedName("auto_matching_info")
    var autoMatchingInfo: AutoMatchingInfo? = null

    @Ignore
    @SerializedName("manual_matching_info")
    var manualMatchingInfo: ManualMatchingInfo? = null

    @Ignore
    @SerializedName("match_score")
    var matchingScore: Double? = null

    @Ignore
    @SerializedName("account_already_exists")
    var accountAlreadyExists: Boolean? = null

    @Ignore
    @SerializedName("manual_verification_status")
    var manualVerificationStatus: ManualMatchingStatus? = null

    fun isAccountSelected() = isSelected != 0
    fun getBankLogoIfAvailable(): String? {
        val currentBank = Bank(bankCode ?: "", "")
        return if (Bank.BANKS.contains(currentBank)) {
            Bank.BANKS.first { it.bankCode == currentBank.bankCode }.logo
        } else {
            null
        }
    }

    fun getActiveAccount(): Boolean = flag.equals(PpobConst.ACTIVE, ignoreCase = true)

    fun getInActiveAccount(): Boolean = flag.equals(PpobConst.IN_ACTIVE, ignoreCase = true)

    fun isMatchingFailure(): Boolean {
        return when {
            autoMatchingInfo?.isAutoNameMatching.isTrue -> {
                autoMatchingInfo?.autoMatchingStatus == AutoMatchingStatus.FAILED
            }
            manualMatchingInfo?.isManualVerification.isTrue -> {
                manualMatchingInfo?.manualMatchingStatus == ManualMatchingStatus.REJECTED
            }
            else -> false
        }
    }

    fun getNotSupportedAccount(): Boolean = flag.equals(PpobConst.NOT_SUPPORTED, ignoreCase = true)

    fun isManualMatchingRequired(): Boolean =
        autoMatchingInfo?.autoMatchingStatus == AutoMatchingStatus.FAILED
                && manualMatchingInfo == null

    fun isManualMatchingInProgress() =
        manualMatchingInfo?.manualMatchingStatus == ManualMatchingStatus.UNVERIFIED

    fun isManualMatchingRejected() =
        manualMatchingInfo?.manualMatchingStatus == ManualMatchingStatus.REJECTED

}

@Parcelize
data class AutoMatchingInfo(
    @Ignore
    @SerializedName("is_auto_name_matching")
    var isAutoNameMatching: Boolean? = false,
    @Ignore
    @SerializedName("auto_matching_status")
    var autoMatchingStatus: AutoMatchingStatus? = null,
    @Ignore
    @SerializedName("matched_name")
    var matchedName: String? = null
): Parcelable

@Parcelize
data class ManualMatchingInfo(
    @Ignore
    @SerializedName("is_manual_verification")
    var isManualVerification: Boolean? = false,
    @Ignore
    @SerializedName("manual_matching_status")
    var manualMatchingStatus: ManualMatchingStatus? = null,
    @Ignore
    @SerializedName("rejected_reason")
    var rejectedReason: String? = null
): Parcelable

enum class AutoMatchingStatus {
    SUCCESS, FAILED
}

enum class ManualMatchingStatus {
    UNVERIFIED, VERIFIED, REJECTED
}

enum class BankAccountStatus {
    VALID, INVALID, MANUAL_VERIFICATION
}

enum class UrlType {
    APPEAL_FLOW, MATCHING_INFO, FAQ_USED_ACCOUNT, FAQ_BLOCKED_ACCOUNT
}