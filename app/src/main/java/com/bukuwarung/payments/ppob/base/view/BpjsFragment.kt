package com.bukuwarung.payments.ppob.base.view

import android.app.Activity
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.viewModels
import com.bukuwarung.R
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.databinding.FragmentBpjsBinding
import com.bukuwarung.payments.CustomerListActivity
import com.bukuwarung.payments.PaymentDownBottomSheet
import com.bukuwarung.payments.bottomsheet.PpobBillDetailsBottomSheet
import com.bukuwarung.payments.constants.PpobConst
import com.bukuwarung.payments.data.model.FinproAddCartRequest
import com.bukuwarung.payments.data.model.FinproBeneficiary
import com.bukuwarung.payments.data.model.FinproOrderResponse
import com.bukuwarung.payments.data.model.ppob.ProfilesItem
import com.bukuwarung.payments.ppob.base.model.PpobEvent
import com.bukuwarung.payments.ppob.base.viewmodel.BpjsViewModel
import com.bukuwarung.ui_component.utils.isNotNullOrEmpty
import com.bukuwarung.utils.DateTimeUtils
import com.bukuwarung.utils.InputUtils
import com.bukuwarung.utils.setSingleClickListener
import com.bukuwarung.utils.showView
import com.github.razir.progressbutton.hideProgress
import com.github.razir.progressbutton.showProgress

class BpjsFragment : BaseFragment(), MonthListDialog.ICommunicator,
    RecentAndFavouriteFragment.IRecentAndFavCommunicator,
    PpobBillDetailsBottomSheet.PpobBillDetailsBsListener {

    private var _binding: FragmentBpjsBinding? = null
    private val binding get() = _binding!!
    private var addToCartResponse: FinproOrderResponse? = null
    private val category = PpobConst.CATEGORY_BPJS
    private var selectedMonth: Int? = null
    private var recentAndFavouriteFragment: RecentAndFavouriteFragment? = null

    private val viewModel: BpjsViewModel by viewModels()

    private val from by lazy { arguments?.getString(FROM).orEmpty() }
    private val accountNumber by lazy { arguments?.getString(ACCOUNT_NUMBER).orEmpty() }
    private val phoneNumber by lazy { arguments?.getString(PHONE_NUMBER).orEmpty() }
    private val code by lazy { arguments?.getString(CODE).orEmpty() }

    companion object {
        private const val FROM = "FROM"
        private const val ACCOUNT_NUMBER = "ACCOUNT_NUMBER"
        private const val PHONE_NUMBER = "PHONE_NUMBER"
        private const val CODE = "CODE"
        fun createIntent(
            from: String,
            accountNumber: String,
            code: String,
            phoneNumber: String
        ): BpjsFragment {
            val bundle = Bundle().apply {
                putString(FROM, from)
                putString(ACCOUNT_NUMBER, accountNumber)
                putString(CODE, code)
                putString(PHONE_NUMBER, phoneNumber)
            }
            return BpjsFragment().apply {
                arguments = bundle
            }
        }
    }

    override fun onCreateView(
        layoutInflater: LayoutInflater,
        viewGroup: ViewGroup?,
        bundle: Bundle?
    ): View {
        _binding = FragmentBpjsBinding.inflate(layoutInflater, viewGroup, false)
        return binding.root
    }

    override fun setupView(view: View) {
        with(binding) {
            recentAndFavouriteFragment = RecentAndFavouriteFragment.createIntent(category).also {
                childFragmentManager.beginTransaction().add(
                    flRecentAndFav.id,
                    it
                ).commit()
            }
            bivBiller.setDropDownDrawable {
                showDialog()
            }
            bivCustomerNumber.onTextChanged {
                bivCustomerNumber.setClearDrawable()
                viewModel.checkCustomerNumberValidation(
                    bivCustomerNumber.getText(),
                    requireContext()
                )
            }
            bivNumber.onTextChanged { bivNumber.setClearDrawable() }
            ivContact.setSingleClickListener {
                resultLauncher.launch(
                    CustomerListActivity.createIntent(
                        requireContext(),
                        PaymentConst.TYPE_PAYMENT_OUT,
                        AnalyticsConst.PPOB_VEHICLE_TAX,
                        true
                    )
                )
            }
            btnCek.setSingleClickListener {
                InputUtils.hideKeyBoardWithCheck(requireActivity())
                addToCart()
            }
            if (accountNumber.isNotNullOrEmpty()) {
                showViewAndSetData(accountNumber, code, phoneNumber)
            }
        }
    }

    private fun showViewAndSetData(accountNumber: String, code: String, phoneNumber: String) =
        with(binding) {
            bivCustomerNumber.showView()
            bivNumber.showView()
            ivContact.showView()
            btnCek.showView()
            bivCustomerNumber.setText(accountNumber)
            bivBiller.setText(code)
            bivNumber.setText(phoneNumber)
            selectedMonth = DateTimeUtils.getMonthIntFromMonthName(code) + 1
            btnCek.callOnClick()
        }

    private fun addToCart() {
        viewModel.addToCart(
            FinproAddCartRequest(
                sku = "",
                beneficiary = FinproBeneficiary(
                    category = category,
                    accountNumber = binding.bivCustomerNumber.getText(),
                    code = "",
                    phoneNumber = binding.bivNumber.getText()
                ),
                month = selectedMonth,
                userType = PpobConst.USERTYPE_TREATMENT
            )
        )
    }

    private fun showDialog() {
        InputUtils.hideKeyboard(requireActivity())
        MonthListDialog().show(childFragmentManager, MonthListDialog.TAG)
    }

    override fun subscribeState() {
        viewModel.observeEvent.observe(viewLifecycleOwner) {
            when (it) {
                is PpobEvent.ToDetail -> {
                    addToCartResponse = it.orderDetail
                    trackFetchBillEvent()
                    binding.bivCustomerNumber.setSuccessState("")
                    showBillDetailsBottomSheet()
                }
                is PpobEvent.ServerError -> {
                    trackFetchBillEvent(it.message.orEmpty())
                    binding.bivCustomerNumber.setSuccessState("")
                    showPaymentDownBottomSheet(true, it.message)
                }
                is PpobEvent.InternetError -> {
                    trackFetchBillEvent(it.message.orEmpty())
                    binding.bivCustomerNumber.setSuccessState("")
                    showPaymentDownBottomSheet(false, it.message)
                }
                is PpobEvent.OtherError -> {
                    trackFetchBillEvent(it.message.orEmpty())
                    binding.bivCustomerNumber.setErrorMessage(it.message.orEmpty())
                }
                else -> {
                    binding.bivCustomerNumber.setSuccessState("")
                }
            }
        }
        viewModel.viewState.observe(viewLifecycleOwner) {
            if (it.showLoading) {
                binding.btnCek.showProgress {
                    buttonTextRes = null
                    progressColor = Color.BLACK
                }
            } else {
                binding.btnCek.hideProgress(R.string.bt_cek)
            }
            if (it.numberLengthInvalid) {
                binding.bivCustomerNumber.setErrorMessage(it.errorMessage)
                binding.btnCek.isEnabled = false
            } else if (binding.bivCustomerNumber.getText().isNotBlank()) {
                binding.bivCustomerNumber.setSuccessState("")
                binding.btnCek.isEnabled = true
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding?.bivCustomerNumber?.removeTextChanged()
        _binding = null
    }

    private fun showPaymentDownBottomSheet(isServiceDown: Boolean, message: String? = null) {
        val paymentDownBottomSheet = PaymentDownBottomSheet.createInstance(isServiceDown, message)
        paymentDownBottomSheet.show(childFragmentManager, PaymentDownBottomSheet.TAG)
    }

    private fun trackFetchBillEvent(errorMessage: String = "") {
        AppAnalytics.trackEvent(
            AnalyticsConst.EVENT_PPOB_FETCH_BILL,
            AppAnalytics.PropBuilder()
                .put(
                    AnalyticsConst.ID,
                    binding.bivCustomerNumber.getText()
                )
                .put(AnalyticsConst.PHONE_FILLED, binding.bivNumber.getText())
                .put(AnalyticsConst.MONTH, binding.bivBiller.getText())
                .put(AnalyticsConst.TYPE, AnalyticsConst.PPOB_BPJS)
                .put(
                    AnalyticsConst.STATUS,
                    if (errorMessage.isBlank()) AnalyticsConst.STATUS_SUCCESS else AnalyticsConst.STATUS_FAILED
                )
                .put(AnalyticsConst.REASON, errorMessage)
        )
    }

    private fun showBillDetailsBottomSheet() {
        val ppobBillDetailsBottomSheet =
            PpobBillDetailsBottomSheet.createInstance(addToCartResponse, category)
        ppobBillDetailsBottomSheet.show(childFragmentManager, PpobBillDetailsBottomSheet.TAG)
    }

    override fun setData(profilesItem: ProfilesItem) {
        showViewAndSetData(
            profilesItem.details?.accountNumber.orEmpty(),
            profilesItem.biller?.code.orEmpty(),
            profilesItem.details?.phoneNumber.orEmpty()
        )
    }

    override fun setSelectedMonth(selectedMonth: String) {
        this.selectedMonth = DateTimeUtils.getMonthIntFromMonthName(selectedMonth) + 1
        if (binding.bivCustomerNumber.getText()
                .isNotBlank() && viewModel.checkCustomerNumberValidation(
                binding.bivCustomerNumber.getText(),
                requireContext()
            )
        )
            addToCart()
        binding.bivBiller.setText(selectedMonth)
        binding.bivCustomerNumber.showView()
        binding.bivNumber.showView()
        binding.ivContact.showView()
        binding.btnCek.showView()
    }

    override fun refreshFavouritesTab() {
        recentAndFavouriteFragment?.refreshFavAndRecentTab()
    }

    private val resultLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                val data: Intent? = result.data
                var phoneNumber =
                    data?.getStringExtra(CustomerListActivity.PHONE_NUMBER).orEmpty()
                if (phoneNumber.startsWith("8")) phoneNumber = "0$phoneNumber"
                binding.bivNumber.setText(phoneNumber)
                binding.bivNumber.setSelection()
            }
        }
}
