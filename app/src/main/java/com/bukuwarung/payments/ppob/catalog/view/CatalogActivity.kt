package com.bukuwarung.payments.ppob.catalog.view

import android.app.ActionBar
import android.content.Context
import android.content.Intent
import android.view.Gravity
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.activity.viewModels
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.R
import com.bukuwarung.activities.superclasses.BaseActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.databinding.ActivityCatalogBinding
import com.bukuwarung.payments.PaymentDownBottomSheet
import com.bukuwarung.payments.constants.PpobConst
import com.bukuwarung.payments.data.model.ppob.PricingType
import com.bukuwarung.payments.data.model.ppob.ProductsItem
import com.bukuwarung.payments.data.model.ppob.ValuesItem
import com.bukuwarung.payments.ppob.catalog.adapter.BannerAdapter
import com.bukuwarung.payments.ppob.catalog.adapter.CatalogAdapter
import com.bukuwarung.payments.ppob.catalog.adapter.CatalogExpandableAdapter
import com.bukuwarung.payments.ppob.catalog.adapter.CatalogWithoutAdminFeeAdapter
import com.bukuwarung.payments.ppob.catalog.adapter.CategoryAdapter
import com.bukuwarung.payments.ppob.catalog.view.SetSellingPriceBottomSheet.Companion.SOURCE_ATUR
import com.bukuwarung.payments.ppob.catalog.view.SetSellingPriceBottomSheet.Companion.SOURCE_ATUR_HARGA
import com.bukuwarung.payments.ppob.catalog.viewmodel.PpobCatalogViewModel
import com.bukuwarung.ui_component.base.BaseErrorView
import com.bukuwarung.utils.InputUtils
import com.bukuwarung.utils.asVisibility
import com.bukuwarung.utils.dp
import com.bukuwarung.utils.hideView
import com.bukuwarung.utils.isTrue
import com.bukuwarung.utils.setSingleClickListener
import com.bukuwarung.utils.showView
import com.bukuwarung.widget.ExpandableRecyclerAdapter
import com.google.android.material.snackbar.Snackbar
import com.google.android.material.snackbar.SnackbarContentLayout
import com.google.android.material.tabs.TabLayoutMediator
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class CatalogActivity : BaseActivity(),
    PaymentDownBottomSheet.PaymentDownBsListener {
    private lateinit var binding: ActivityCatalogBinding
    private val viewModel: PpobCatalogViewModel by viewModels()
    private lateinit var adapter: CatalogExpandableAdapter
    private lateinit var categoryAdapter: CategoryAdapter
    private val categoryList = ArrayList<ValuesItem>()
    private val bannerAdapter by lazy {
        BannerAdapter(clickAction = { bannerType -> onBannerClick(bannerType) })
    }
    private var selectedCategory: String? = null

    companion object {
        private const val IS_PRICE_CHANGED = "IS_PRICE_CHANGED"
        fun createIntent(context: Context, isPriceChanged: Boolean = false): Intent = Intent(context, CatalogActivity::class.java).apply {
            putExtra(IS_PRICE_CHANGED, isPriceChanged)
        }
    }

    override fun setViewBinding() {
        binding = ActivityCatalogBinding.inflate(layoutInflater)
        setContentView(binding.root)
    }

    override fun setupView() {
        setToolBarView()
        setUpBanners()
        binding.bukuErrorView.addCallback(errorViewCallBack)
        binding.rvList.layoutManager = LinearLayoutManager(this)

        viewModel.getAllCategories()
        if (intent.getBooleanExtra(IS_PRICE_CHANGED, false).isTrue) {
            showSnackBar()
        }
        setDefaultVisibilityForAmountView()
    }

    override fun subscribeState() {
        viewModel.observeEvent.observe(this) {
            with(binding){
                when (it) {
                    is PpobCatalogViewModel.Event.ShowFilter -> {
                        it.categoryList.categoryGroups?.forEach { categoryGroup ->
                            categoryGroup.values?.forEach { item ->
                                categoryList.add(item)
                            }
                        }
                        rvFilter.layoutManager = LinearLayoutManager(this@CatalogActivity, RecyclerView.HORIZONTAL, false)
                        categoryAdapter = CategoryAdapter(categoryList, ::clickFilter)
                        rvFilter.adapter = categoryAdapter
                        progressBar.hideView()
                        bukuErrorView.hideView()
                        ivFilter.showView()
                        rvFilter.showView()
                        amountView.hideView()
                        ivFilter.setSingleClickListener {
                            CatalogFilterDialog(this@CatalogActivity, it.categoryList.categoryGroups, ::clickFilter).show()
                        }
                    }
                    is PpobCatalogViewModel.Event.ShowBillerList -> {
                        if (it.catalogListResponse.flags?.useBillerCollapse.isTrue) {
                            togglePostPaidViewVisibility(false)
                            adapter = CatalogExpandableAdapter(clickAction = { product -> changePrice(product) })
                            binding.rvList.adapter = adapter
                            val adapterList = ArrayList<CatalogExpandableAdapter.BillersListItem?>()
                            it.catalogListResponse.billers?.forEach { billerItem ->
                                adapterList.add(CatalogExpandableAdapter.BillersListItem(itemType = ExpandableRecyclerAdapter.TYPE_HEADER, billersItem = billerItem))
                                billerItem.products?.forEach { productItem ->
                                    adapterList.add(CatalogExpandableAdapter.BillersListItem(itemType = CatalogExpandableAdapter.TYPE_CONTENT, product = productItem))
                                }
                            }
                            adapter.setItems(adapterList, 0)
                            showBanners(it.catalogListResponse.flags?.isReadyForExport)
                        } else {
                            if (it.catalogListResponse.flags?.directInputBox.isTrue) {
                                togglePostPaidViewVisibility(true)
                                setProductDetails(
                                    it.catalogListResponse.billers?.getOrNull(0)?.products?.getOrNull(0),
                                    viewModel.pricingType,
                                    viewModel.stepChange,
                                    viewModel.categoryItem?.categoryCode.orEmpty()
                                )
                            } else {
                                togglePostPaidViewVisibility(false)
                                val adapterList = ArrayList<ProductsItem>()
                                it.catalogListResponse.billers?.forEach { billerItem ->
                                    billerItem.products?.forEach { productItem ->
                                        adapterList.add(productItem)
                                    }
                                }
                                val adapter = if (it.catalogListResponse.flags?.showAdminFee.isTrue) {
                                    CatalogAdapter(adapterList, clickAction = { product -> changePrice(product) })
                                } else {
                                    CatalogWithoutAdminFeeAdapter(adapterList, clickAction = { product -> changePrice(product) })
                                }
                                rvList.adapter = adapter
                                showBanners(it.catalogListResponse.flags?.isReadyForExport)
                            }
                        }
                        progressBar.hideView()
                        bukuErrorView.hideView()
                    }
                    is PpobCatalogViewModel.Event.ShowLoader -> {
                        progressBar.showView()
                        rvList.hideView()
                        grpBanner.hideView()
                        bukuErrorView.hideView()
                        amountView.hideView()
                    }
                    is PpobCatalogViewModel.Event.ShowInternetError -> {
                        progressBar.hideView()
                        rvList.hideView()
                        grpBanner.hideView()
                        amountView.hideView()
                        selectedCategory = it.category
                        bukuErrorView.showView()
                        bukuErrorView.setErrorType(
                            BaseErrorView.Companion.ErrorType.CUSTOM,
                            getString(R.string.no_connection_title),
                            getString(R.string.no_connection_message),
                            getString(R.string.reload), R.drawable.ic_no_inet
                        )
                        if (it.category.isNullOrBlank()){
                            ivFilter.hideView()
                            rvFilter.hideView()
                        }
                    }
                    is PpobCatalogViewModel.Event.ShowServerError -> {
                        progressBar.hideView()
                        rvList.hideView()
                        grpBanner.hideView()
                        amountView.hideView()
                        selectedCategory = it.category
                        bukuErrorView.showView()
                        bukuErrorView.setErrorType(
                            BaseErrorView.Companion.ErrorType.CUSTOM,
                            getString(R.string.sorry_disturbance),
                            getString(R.string.try_later),
                            getString(R.string.reload), R.drawable.ic_no_inet
                        )
                        if (it.category.isNullOrBlank()){
                            ivFilter.hideView()
                            rvFilter.hideView()
                        }
                    }
                    is PpobCatalogViewModel.Event.ShowServerErrorBottomSheet -> {
                        showPaymentDownBottomSheet(true, it.message)
                    }
                    is PpobCatalogViewModel.Event.ShowInternetErrorBottomSheet -> {
                        showPaymentDownBottomSheet(false)
                    }
                    is PpobCatalogViewModel.Event.SellingPriceUpdated ->{
                        if (it.screenType == PpobConst.SCREEN_CATALOG_ACTIVITY) {
                            it.category?.let { category -> viewModel.getBillersList(category) }
                            handlePriceUpdated()
                        }
                    }
                    else -> {}
                }
            }
        }
    }

    private var errorViewCallBack = object : BaseErrorView.Callback {
        override fun ctaClicked() {
            selectedCategory?.let { category ->
                viewModel.getBillersList(category)
            } ?: run {
                viewModel.getAllCategories()
            }
        }

        override fun messageClicked() {}

    }

    private fun showBanners(showPromotionBanner: Boolean?) {
        val bannersList =
            arrayListOf<PpobCatalogViewModel.BannerType>(PpobCatalogViewModel.BannerType.BannerAtur)
        if (showPromotionBanner.isTrue)
            bannersList.add(PpobCatalogViewModel.BannerType.BannerPromotion)
        bannerAdapter.setItem(bannersList)
    }

    private fun onBannerClick(bannerType: PpobCatalogViewModel.BannerType){
        when (bannerType) {
            is PpobCatalogViewModel.BannerType.BannerAtur -> { //atur click action
                viewModel.setCurrentProduct(ProductsItem(productName = viewModel.categoryItem?.displayName))
                showSellingPriceBottomSheet(SOURCE_ATUR)
            }
            else -> {
                trackPromotionEvent()
                startActivity(PromotionActivity.createIntent(this@CatalogActivity))
            }
        }
    }

    private fun togglePostPaidViewVisibility(showAmountView:Boolean) {
        binding.amountView.visibility = showAmountView.asVisibility()
        binding.rvList.visibility = (!showAmountView).asVisibility()
        binding.grpBanner.visibility = (!showAmountView).asVisibility()
    }

    private fun setProductDetails(
        productItem: ProductsItem?,
        pricingType: PricingType,
        stepChange: Double,
        category: String
    ) {
        with(binding.amountView) {
            productItem?.let {
                showView()
                setProductDetails(productItem, pricingType, stepChange, 0.0, 0.0, SOURCE_ATUR_HARGA)
                binding.btnSubmit.setSingleClickListener {
                    viewModel.setSellingPrice(
                        getTotalAmount().toDouble(),
                        pricingType, binding.swRounding.isChecked, category,
                        code = null, PpobConst.SCREEN_CATALOG_ACTIVITY
                    )
                }
            }?: run{
                hideView()
            }
        }
    }

    private fun handlePriceUpdated() {
        with(binding.amountView) {
            updateSellingPrice()
            toggleSubmitButtonVisibility(false)
            toggleSubmittedTextViewVisibility(true)
        }
    }

    fun setToolBarView() {
        with(binding.includeToolBar) {
            tbPpob.navigationIcon =
                    ContextCompat.getDrawable(this@CatalogActivity, R.drawable.ic_arrow_back)
            toolBarLabel.text = getString(R.string.ppob_set_sellling_price)
            tbPpob.setNavigationOnClickListener {
                InputUtils.hideKeyBoardWithCheck(this@CatalogActivity)
                onBackPressed()
            }
            ivHelp.hideView()
            tvHelp.hideView()
        }
    }

    fun changePrice(productsItem: ProductsItem) {
        trackSellingPriceEvent(productsItem)
        viewModel.setCurrentProduct(productsItem)
        showSellingPriceBottomSheet(SOURCE_ATUR_HARGA)
    }

    fun clickFilter(category: String) {
        val index = categoryList.indexOf(categoryList.filter { it.categoryCode == category }.getOrNull(0))
        InputUtils.hideKeyBoardWithCheck(this@CatalogActivity)
        viewModel.categoryItem = categoryList[index]
        categoryAdapter.notifyDataSetChanged()
        binding.rvFilter.scrollToPosition(index)
        viewModel.getBillersList(category)
    }

    private fun showSnackBar() {
        val snackBar = Snackbar.make(binding.root, R.string.change_in_capital_price, Snackbar.LENGTH_SHORT)
        snackBar.setTextColor(ContextCompat.getColor(this, R.color.yellow_60))
        val imgClose = ImageView(this)
        imgClose.scaleType = ImageView.ScaleType.CENTER_INSIDE
        val layImageParams = ViewGroup.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.MATCH_PARENT)
        imgClose.setImageResource(R.drawable.ic_close_yellow)
        val textViewAction = snackBar.view.findViewById(R.id.snackbar_action) as TextView
        (textViewAction.parent as SnackbarContentLayout).addView(imgClose, layImageParams)
        imgClose.setOnClickListener { snackBar.dismiss() }
        snackBar.setBackgroundTint(ContextCompat.getColor(this, R.color.yellow_5))
        val layoutParams = ActionBar.LayoutParams(snackBar.view.layoutParams)
        layoutParams.gravity = Gravity.TOP
        layoutParams.setMargins(0, 56.dp, 0, 0)
        snackBar.view.layoutParams = layoutParams
        snackBar.show()
    }

    private fun showPaymentDownBottomSheet(isServiceDown: Boolean, message: String? = null) {
        PaymentDownBottomSheet.createInstance(isServiceDown, message).showNow(supportFragmentManager, PaymentDownBottomSheet.TAG)
    }

    private fun showSellingPriceBottomSheet(source: Int) {
        SetSellingPriceBottomSheet.createIntent(source).show(supportFragmentManager, SetSellingPriceBottomSheet.TAG)
    }

    override fun onButtonClicked() {
        showSellingPriceBottomSheet(SOURCE_ATUR_HARGA)
    }

    private fun setDefaultVisibilityForAmountView() = binding.amountView.apply{
        toggleArrowButtonsVisibility(false)
    }

    private fun setUpBanners() {
        with(binding.vpBanner) {
            clipToPadding = false
            clipChildren = false
            offscreenPageLimit = 2
            adapter = bannerAdapter
        }
        TabLayoutMediator(binding.tbBanner, binding.vpBanner) { tab, position ->
        }.attach()
    }

    private fun trackSellingPriceEvent(productItem: ProductsItem) {
        AppAnalytics.trackEvent(
            AnalyticsConst.SET_PPOB_SELLING_PRICE,
            AppAnalytics.PropBuilder().apply {
                put(
                    AnalyticsConst.TYPE, viewModel.categoryItem?.categoryCode
                )
                put(AnalyticsConst.PRODUCT, productItem.productName)
                put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.SET_SELLING_PRICE_PAGE)
            }, true, false, true
        )
    }

    private fun trackPromotionEvent() {
        AppAnalytics.trackEvent(
            AnalyticsConst.EVENT_CREATE_CATALOG,
            AppAnalytics.PropBuilder().apply {
                put(
                    AnalyticsConst.TYPE, viewModel.categoryItem?.categoryCode
                )
            }, true, false, true
        )
    }
}