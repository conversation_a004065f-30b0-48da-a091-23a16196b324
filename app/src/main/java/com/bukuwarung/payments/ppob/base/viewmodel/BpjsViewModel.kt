package com.bukuwarung.payments.ppob.base.viewmodel

import android.content.Context
import com.bukuwarung.R
import com.bukuwarung.domain.business.BusinessUseCase
import com.bukuwarung.domain.payments.FinproUseCase
import com.bukuwarung.session.SessionManager
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class BpjsViewModel @Inject constructor(
    finproUseCase: FinproUseCase,
    sessionManager: SessionManager,
    businessUseCase: BusinessUseCase
) : PpobViewModel(finproUseCase, sessionManager, businessUseCase) {

    fun checkCustomerNumberValidation(
        customerNumber: String = "",
        context: Context
    ): Boolean {
        val numberLengthInvalid: Boolean = customerNumber.length !in 11..16
        viewState.value = currentViewState()?.copy(
            numberLengthInvalid = numberLengthInvalid,
            errorMessage = context.getString(R.string.invalid_card_number)
        )
        return numberLengthInvalid
    }
}