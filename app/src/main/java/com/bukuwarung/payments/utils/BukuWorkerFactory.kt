package com.bukuwarung.payments.utils

import android.content.Context
import androidx.work.ListenableWorker
import androidx.work.WorkerFactory
import androidx.work.WorkerParameters
import com.bukuwarung.activities.notification.worker.PushFCMTokenWorkerFactory
import com.bukuwarung.activities.settings.workmanager.ManualSyncWorkerFactory
import javax.inject.Inject

class BukuWorkerFactory @Inject constructor(
    private val bankAccountSyncWorkerFactory: BankAccountSyncWorker.Factory,
    private val manualSyncWorkerFactory: ManualSyncWorkerFactory,
    private val pushFCMTokenWorkerFactory: PushFCMTokenWorkerFactory
) : WorkerFactory() {

    override fun createWorker(
        appContext: Context,
        workerClassName: String,
        workerParameters: WorkerParameters
    ): ListenableWorker? {
        return when (workerClassName) {
            BankAccountSyncWorker::class.java.name ->
                bankAccountSyncWorkerFactory.create(appContext, workerParameters)
            else -> {
                // Try other factories
                manualSyncWorkerFactory.createWorker(appContext, workerClassName, workerParameters)
                    ?: pushFCMTokenWorkerFactory.createWorker(appContext, workerClassName, workerParameters)
            }
        }
    }
}