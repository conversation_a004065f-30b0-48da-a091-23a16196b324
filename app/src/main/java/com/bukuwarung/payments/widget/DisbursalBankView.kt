package com.bukuwarung.payments.widget

import android.content.Context
import android.graphics.Typeface
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import com.bukuwarung.R
import com.bukuwarung.database.entity.Bank
import com.bukuwarung.databinding.LayoutDisbursalBankViewBinding
import com.bukuwarung.payments.data.model.FinproOrderResponse
import com.bukuwarung.payments.data.model.PaymentHistory
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.hideView
import com.bukuwarung.utils.showView
import com.bumptech.glide.Glide


class DisbursalBankView : ConstraintLayout {

    constructor(context: Context) : super(context)

    constructor(context: Context, attrs: AttributeSet) : super(context, attrs)

    constructor(context: Context, attrs: AttributeSet, defStyleAttr: Int) :
            super(context, attrs, defStyleAttr)

    private val binding =
        LayoutDisbursalBankViewBinding.inflate(LayoutInflater.from(context), this, true)

    init {
        binding.root.hideView()
    }

    interface Callback {
        fun setDisbursalBank(bankIds: List<String>?)
    }

    fun setView(order: FinproOrderResponse, callback: Callback) {
        if (order.invalidDisburableAccounts.isNullOrEmpty()) {
            binding.root.hideView()
            return
        }
        with(binding) {
            when (order.status) {
                // Visibility changing views - tvMessage, tvSetBank, includeBankLayout, tvEdit,
                PaymentHistory.STATUS_FAILED -> {
                    // Add checks for disbursal bank info
                    // If we have the disbursal bank, next we check for retry attempts counter
                    root.showView()
                    tvTitle.setTextColor(ContextCompat.getColor(context, R.color.out_red))
                    if (order.invalidDisburableAccounts.size == 1) {
                        // Payment failed, disbursement was automatically attempted once
                        tvTitle.text =
                            context.getString(R.string.change_beneficiary_account)
                        tvMessage.text =
                            context.getString(R.string.change_beneficiary_account_message)
                        tvSetBank.setOnClickListener {
                            callback.setDisbursalBank(order.invalidDisburableAccounts)
                        }
                        tvMessage.showView()
                        tvSetBank.showView()
                        includeBankLayout.root.hideView()
                    } else {
                        // Retry attempts are exhausted
                        val config = RemoteConfigUtils.getPaymentConfigs()
                        if (order.invalidDisburableAccounts.size > config.disbursalMaxRetryAttempts) {
                            // Show contact to CS message
                            layoutDisbursalBank.setBackgroundResource(
                                R.drawable.bg_solid_red5_corner_8dp
                            )
                            tvTitle.text = context.getString(R.string.retransaction_failed)
                            tvMessage.apply {
                                text = context.getString(R.string.contact_support_for_disbursal)
                                setTextColor(ContextCompat.getColor(context, R.color.black_40))
                            }
                            tvMessage.showView()
                            tvSetBank.hideView()
                            includeBankLayout.root.hideView()
                        } else {
                            // Let user change the disbursement bank
                            order.items?.firstOrNull()?.beneficiary?.let {
                                layoutDisbursalBank.setBackgroundResource(
                                    R.drawable.bg_solid_red5_corner_8dp
                                )
                                tvTitle.text =
                                    context.getString(R.string.payment_to_account_failed_try_another)
                                with(includeBankLayout) {
                                    tvBankName.text = Utility.dashDividedString(it.code, it.name)
                                    tvAccountNumber.text = it.accountNumber
                                    val logoUrl =
                                        Bank.BANKS.firstOrNull { bank -> bank.bankCode == it.code }?.logo
                                    Glide.with(context)
                                        .load(logoUrl)
                                        .placeholder(R.drawable.ic_bank)
                                        .error(R.drawable.ic_bank)
                                        .into(ivBank)
                                    tvEdit.apply {
                                        setOnClickListener {
                                            callback.setDisbursalBank(order.invalidDisburableAccounts)
                                        }
                                        setTextColor(
                                            ContextCompat.getColor(context, R.color.out_red)
                                        )
                                        typeface = Typeface.DEFAULT_BOLD
                                        showView()
                                    }
                                }
                                tvMessage.hideView()
                                tvSetBank.hideView()
                                includeBankLayout.root.showView()
                            } ?: run {
                                root.hideView()
                            }
                        }
                    }
                }

                PaymentHistory.STATUS_PAID -> {
                    order.items?.firstOrNull()?.beneficiary?.let {
                        layoutDisbursalBank.setBackgroundResource(
                            R.drawable.bg_rounded_rectangle_blue_5
                        )
                        tvTitle.text = context.getString(R.string.payment_will_be_forwarded_to)
                        tvTitle.setTextColor(ContextCompat.getColor(context, R.color.black_80))
                        with(includeBankLayout) {
                            tvBankName.text = Utility.dashDividedString(it.code, it.name)
                            tvAccountNumber.text = it.accountNumber
                            tvEdit.hideView()
                            val logoUrl = Bank.BANKS.firstOrNull { bank -> bank.bankCode == it.code }?.logo
                            Glide.with(context)
                                .load(logoUrl)
                                .placeholder(R.drawable.ic_bank)
                                .error(R.drawable.ic_bank)
                                .into(ivBank)
                        }
                        tvMessage.hideView()
                        tvSetBank.hideView()
                        includeBankLayout.root.showView()
                        root.showView()
                    } ?: run {
                        root.hideView()
                    }
                }

                PaymentHistory.STATUS_COMPLETED -> {
                    // Only when COMPLETED after the failure and refund was done.
                    order.items?.firstOrNull()?.beneficiary?.let {
                        layoutDisbursalBank.setBackgroundResource(
                            R.drawable.bg_rounded_rectangle_green_5
                        )
                        tvTitle.text =
                            context.getString(R.string.payment_successfully_forwarded_to)
                        tvTitle.setTextColor(
                            ContextCompat.getColor(context, R.color.green_80)
                        )
                        with(includeBankLayout) {
                            tvBankName.text = Utility.dashDividedString(it.code, it.name)
                            tvAccountNumber.text = it.accountNumber
                            tvEdit.hideView()
                            val logoUrl = Bank.BANKS.firstOrNull { bank -> bank.bankCode == it.code }?.logo
                            Glide.with(context)
                                .load(logoUrl)
                                .placeholder(R.drawable.ic_bank)
                                .error(R.drawable.ic_bank)
                                .into(ivBank)
                        }
                        tvMessage.hideView()
                        tvSetBank.hideView()
                        includeBankLayout.root.showView()
                        root.showView()
                    } ?: run {
                        root.hideView()
                    }
                }

                else -> {
                    root.hideView()
                }
            }
        }
    }
}
