package com.bukuwarung.payments.addbank

import android.app.ActionBar
import android.app.Activity
import android.content.Intent
import android.os.Handler
import android.os.Looper
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.activity.viewModels
import androidx.core.content.ContextCompat
import com.bukuwarung.R
import com.bukuwarung.activities.WebviewActivity
import com.bukuwarung.activities.profile.update.BusinessProfileFormActivity
import com.bukuwarung.activities.referral.main_referral.dialogs.NullProfileReferralDialog
import com.bukuwarung.activities.superclasses.BaseActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.analytics.SurvicateAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.database.entity.Bank
import com.bukuwarung.database.entity.BankAccount
import com.bukuwarung.database.entity.ManualMatchingStatus
import com.bukuwarung.database.entity.RefundBankAccount
import com.bukuwarung.databinding.ActivityAddBankAccountBinding
import com.bukuwarung.payments.PaymentDownBottomSheet
import com.bukuwarung.payments.bottomsheet.AddUsedBankBottomSheet
import com.bukuwarung.payments.constants.PpobConst
import com.bukuwarung.payments.data.model.BankValidationRequest
import com.bukuwarung.payments.selectbank.SelectBankActivity
import com.bukuwarung.payments.widget.BankAccountView
import com.bukuwarung.payments.widget.ProcessViewBottomSheet
import com.bukuwarung.preference.OnboardingPrefManager
import com.bukuwarung.session.SessionManager
import com.bukuwarung.tutor.shape.FocusGravity
import com.bukuwarung.tutor.shape.ShapeType
import com.bukuwarung.tutor.view.OnboardingWidget
import com.bukuwarung.utils.InputUtils
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.Utilities
import com.bukuwarung.utils.afterTextChanged
import com.bukuwarung.utils.asVisibility
import com.bukuwarung.utils.dp
import com.bukuwarung.utils.getColorCompat
import com.bukuwarung.utils.isFalse
import com.bukuwarung.utils.isTrue
import com.bukuwarung.utils.toBoolean
import com.bukuwarung.utils.toInt
import com.bukuwarung.utils.visibleIfTrue
import com.google.android.material.snackbar.Snackbar
import com.google.android.material.snackbar.SnackbarContentLayout
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@AndroidEntryPoint
class AddBankAccountActivity : BaseActivity(), OnboardingWidget.OnboardingWidgetListener {
    private lateinit var binding: ActivityAddBankAccountBinding
    private val scope = MainScope()

    companion object {
        private const val isRedirectFromPaymentsToProfile = "isRedirectFromPaymentsToProfile"
        private const val PAYMENT_TYPE = "paymentType"
        private const val ENTRY_POINT = "entryPoint"
        private const val BOOK_ID = "bookId"
        private const val CUSTOMER_ID = "customerId"
        private const val HAS_BANK_ACCOUNT = "hasBankAccount"
        private const val BANK_TYPE = "bank_type"
        private const val PRODUCT = "product"
        private const val TRANSACTION_ID = "transaction_id"
        private const val SETTING_FIRST_TIME = "setting_first_time"
        private const val AMOUNT = "amount"
        private const val SET_QRIS_BANK = "set_qris_bank"
        private const val SHOW_TUTORIAL = "showTutorial"
        private const val IS_PAYMENT_IN = "is_payment_in"
        private const val ADDING_QRIS_BANK_FOR = "adding_qris_bank_for"
        const val BANK_ACCOUNT = "bankAccount"
        const val REFUND_BANK_ACCOUNT = "refundBankAccount"
        private const val RC_SELECT_BANK = 98
        private const val RC_START_BUSINESS_PROFILE = 96
        private const val REDIRECTION_URL = "redirection_url"

        fun createIntent(
            origin: Activity, paymentType: String, bookId: String?, entryPoint: String,
            customerId: String? = null, hasBankAccount: String? = "false",
            showTutorial: String? = "false", paymentIn: Boolean = false,
            settingFirstTime: Boolean = false, bankType: String? = null, amount: Double? = null,
            setQrisBank: Boolean = true, product: String? = null, transactionId: String? = null,
            addingBankAccountFor: PaymentConst.BankAccountOwner = PaymentConst.BankAccountOwner.SELF,
        ): Intent {
            val i = Intent(origin, AddBankAccountActivity::class.java)
            i.putExtra(PAYMENT_TYPE, paymentType)
            i.putExtra(ENTRY_POINT, entryPoint)
            i.putExtra(BOOK_ID, bookId)
            i.putExtra(CUSTOMER_ID, customerId)
            i.putExtra(HAS_BANK_ACCOUNT, hasBankAccount)
            i.putExtra(SHOW_TUTORIAL, showTutorial)
            i.putExtra(IS_PAYMENT_IN, paymentIn)
            i.putExtra(BANK_TYPE, bankType)
            i.putExtra(SETTING_FIRST_TIME, settingFirstTime)
            i.putExtra(AMOUNT, amount)
            i.putExtra(SET_QRIS_BANK, setQrisBank)
            i.putExtra(PRODUCT, product)
            i.putExtra(TRANSACTION_ID, transactionId)
            i.putExtra(ADDING_QRIS_BANK_FOR, addingBankAccountFor)
            return i
        }
    }

    private var onboardingWidget: OnboardingWidget? = null
    private var isInputBankCoachmarkShown = false

    private val paymentType by lazy { intent?.getStringExtra(PAYMENT_TYPE)?.toInt(0) ?: 0 }
    private var entryPoint = ""
    private val bookId by lazy {
        intent?.getStringExtra(BOOK_ID) ?: SessionManager.getInstance().businessId
    }
    private val customerId by lazy { intent?.getStringExtra(CUSTOMER_ID) }
    private val showTutorial by lazy { intent?.getStringExtra(SHOW_TUTORIAL).toBoolean() }
    private val hasBankAccount by lazy { intent?.getStringExtra(HAS_BANK_ACCOUNT).toBoolean() }
    private val bankType by lazy { intent?.getStringExtra(BANK_TYPE) }
    private val amount by lazy { intent?.getDoubleExtra(AMOUNT, 0.0) }
    private val setQrisBank by lazy { intent?.getBooleanExtra(SET_QRIS_BANK, true) ?: true }
    private val product by lazy { intent?.getStringExtra(PRODUCT) }
    private val transactionId by lazy { intent?.getStringExtra(TRANSACTION_ID) }
    private var processingViewHandler: Handler? = null
    private var processingViewBS: ProcessViewBottomSheet? = null
    private val addingQrisBankFor by lazy {
        intent?.getSerializableExtra(ADDING_QRIS_BANK_FOR) as? PaymentConst.BankAccountOwner
            ?: PaymentConst.BankAccountOwner.SELF
    }
    private val redirectionUrl by lazy { intent?.getStringExtra(REDIRECTION_URL).orEmpty()}

    private val viewModel: AddBankAccountViewModel by viewModels()
    override fun setViewBinding() {
        binding = ActivityAddBankAccountBinding.inflate(layoutInflater)
    }

    override fun setupView() {
        setContentView(binding.root)
        val isFromNotif = intent.getStringExtra(AppConst.IS_FROM_NOTIF).toBoolean()
        entryPoint = if (isFromNotif) AnalyticsConst.PUSH_NOTIF
        else intent?.getStringExtra(ENTRY_POINT) ?: ""
        viewModel.init(
            paymentType, entryPoint, customerId, bookId, hasBankAccount, intent?.getBooleanExtra(
                SETTING_FIRST_TIME, false
            ) ?: false,
            product, transactionId
        )
        if(paymentType==PaymentConst.TYPE_PPOB)
            uiChangesForAddingRefundBank()
        binding.toolbar.navigationIcon = ContextCompat.getDrawable(this, R.drawable.ic_arrow_back)
        binding.toolbar.setNavigationOnClickListener {
            InputUtils.hideKeyBoardWithCheck(this)
            onBackPressed()
        }
        binding.buttonVerify.setOnClickListener {
            binding.inputAccountNumber.setTextColor(getColorCompat(R.color.black))
            viewModel.verifyBankAccount(addingQrisBankFor)
            InputUtils.hideKeyBoardWithCheck(this)
        }
        binding.inputAccountNumber.afterTextChanged {
            binding.txtLabelAccountNumber.setTextColor(getColorCompat(R.color.black_60))
            binding.inputAccountNumber.setTextColor(getColorCompat(R.color.blue_60))
            binding.layoutInputAccountNumber.boxStrokeColor = getColorCompat(R.color.blue_60)
            viewModel.onAccountNumberChanged(it)
        }
        binding.buttonSelectBank.setOnClickListener {
            startActivityForResult(SelectBankActivity.createIntent(this, false, bankType, amount ?: 0.0), RC_SELECT_BANK)
        }
        binding.toolBarMenu.setOnClickListener { openHelp() }
        binding.buttonSaveAccount.setOnClickListener {
            when {
                viewModel.isPaymentPpob() -> {
                    viewModel.addRefundBankAccount(binding.cbSetDefaultRefundAccount.isChecked)
                }
                viewModel.isForQris() && RemoteConfigUtils.getPaymentConfigs().enableNameMatching.isTrue -> {
                // For adding QRIS bank account, we just pass the data back to mweb and mweb will
                // make the API call for creating the bank account.
                    setResultSuccess(viewModel.bankAccount)
                }
                else -> viewModel.addBankAccount(setQrisBank)
            }
        }
        if (showTutorial) showInputBankAccountCoachmark()
        if(paymentType==PaymentConst.TYPE_PPOB)
            uiChangesForAddingRefundBank()
    }

    override fun onDestroy() {
        super.onDestroy()
        processingViewHandler?.removeCallbacksAndMessages(null)
        processingViewHandler = null
    }

    private fun openHelp() {
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_OPEN_TUTORIAL_SCREEN)
        SurvicateAnalytics.invokeEventTracker(AnalyticsConst.EVENT_OPEN_TUTORIAL_SCREEN, this)
        Utilities.launchBrowser(
            this,
            RemoteConfigUtils.getPaymentConfigs().supportUrls.payments
        )
    }

    private fun uiChangesForAddingRefundBank() {
        binding.toolBarLabel.text = getString(R.string.refund_account)
        binding.cbSetDefaultRefundAccount.visibility = View.VISIBLE
    }

    private fun showInputBankAccountCoachmark() {
        if (!isInputBankCoachmarkShown) {
            isInputBankCoachmarkShown = true
            onboardingWidget = OnboardingWidget.createInstance(this, this,
                    OnboardingPrefManager.TUTOR_INPUT_BANK_ACCOUNT_STEP2, binding.tutorInputContainer, R.drawable.onboarding_attention, "",
                    getString(R.string.onboarding_input_bank_account_step2), getString(R.string.understand),
                    FocusGravity.CENTER, ShapeType.RECTANGLE_FULL, 2, 2, sendAnalytics = true, sendAnalyticsOnDismiss = true, delay = 0)
        }
    }

    private fun setResultSuccess(bank: BankAccount?) {
        if(redirectionUrl.isNotBlank()){
            val intent = WebviewActivity.createIntent(this, redirectionUrl, "")
            startActivity(intent)
        } else{
            val i = Intent()
            i.putExtra(BANK_ACCOUNT, bank)
            setResult(Activity.RESULT_OK, i)
        }
        finish()
    }

    private fun addRefundBankSuccess(bank: RefundBankAccount?) {
        val i = Intent()
        i.putParcelableArrayListExtra(REFUND_BANK_ACCOUNT, arrayListOf(bank))
        i.putExtra("bank_name", bank?.bankCode)
        i.putExtra("bank_logo", bank?.getBankLogoIfAvailable())
        i.putExtra("account_number", bank?.accountNumber)
        i.putExtra("user_name", bank?.accountHolderName)
        setResult(Activity.RESULT_OK,i)
        finish()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode != Activity.RESULT_OK && requestCode == RC_START_BUSINESS_PROFILE) finish()
        if (resultCode != Activity.RESULT_OK) return
        when (requestCode) {
            RC_SELECT_BANK -> {
                val bank = data?.getParcelableExtra<Bank>(SelectBankActivity.BANK_RESULT)
                bank?.run {
                    binding.txtBankName.text = bankName
                    viewModel.setSelectedBank(this)
                }
            }
            RC_START_BUSINESS_PROFILE -> {
                viewModel.checkProfileCompletion()
            }
        }
    }

    override fun subscribeState() {
        viewModel.profileIncompleteEvent.observe(this) {
            when (it) {
                is AddBankAccountViewModel.ProfileIncompleteEvent.ShowProfileDialog -> showProfileDialog()
            }
        }
        viewModel.viewState.observe(this) {
            binding.bankAccountView.visibility = (it.showAccountDetails || it.showBlockedError).asVisibility()
            binding.txtErrorNoBank.visibility = it.showBankError.asVisibility()
            binding.buttonVerify.visibility = (!it.verificationLoader).visibleIfTrue()
            binding.txtErrorAccount.visibility = it.showVerificationError.asVisibility()
            binding.buttonSaveAccount.visibility = (!it.addingLoader).asVisibility()
            binding.progressVerificationAdd.visibility = it.addingLoader.asVisibility()
            binding.progressVerification.visibility = it.verificationLoader.asVisibility()
            binding.layoutInputAccountNumber.error = getString(R.string.fragment_add_bank_account_empty_account)
            binding.layoutInputAccountNumber.isErrorEnabled = it.showAccountInputError
            binding.buttonSaveAccount.isEnabled = it.isButtonEnabled
        }
        viewModel.eventStatus.observe(this) {
            when (it) {
                is AddBankAccountViewModel.Event.ShowErrorMessage -> handleErrorApi(it.message, it.code, it.bankDetails)
                is AddBankAccountViewModel.Event.ReturnSelectedAccount -> setResultSuccess(it.currentSelectedAccount)
                is AddBankAccountViewModel.Event.SetResultSuccess -> setResultSuccess(it.bankAccount)
                is AddBankAccountViewModel.Event.AddRefundBankSuccess -> addRefundBankSuccess(it.refundBankAccount)
                is AddBankAccountViewModel.Event.ShowBankAccountDetail -> showBankAccountDetail(it.bankAccount)
                AddBankAccountViewModel.Event.StartTimer -> {
                    processingViewHandler = Handler(Looper.getMainLooper())
                    processingViewHandler?.postDelayed({
                        if (!isFinishing && !isDestroyed) {
                            processingViewBS = ProcessViewBottomSheet.createInstance(
                                getString(R.string.wait_still_processing),
                                getString(R.string.please_dont_close_util_finished)
                            )
                            processingViewBS?.show(supportFragmentManager, ProcessViewBottomSheet.TAG)
                        }
                    }, 3000)
                }
                AddBankAccountViewModel.Event.StopTimer -> {
                    processingViewHandler?.removeCallbacksAndMessages(null)
                    processingViewBS?.dismiss()
                }
            }
        }
    }

    private fun showBankAccountDetail(bankAccount: BankAccount) {
        when {
            bankAccount.matchingStatus.isFalse -> {
                binding.bankAccountView.setBankView(
                    bankAccount, BankAccountView.BankStatus.MATCHING_FAILED,
                    paymentType = paymentType,
                    entryPoint = entryPoint
                )
            }
            bankAccount.accountAlreadyExists.isTrue -> {
                if (bankAccount.manualVerificationStatus == ManualMatchingStatus.VERIFIED) {
                    binding.bankAccountView.setBankView(
                        bankAccount, BankAccountView.BankStatus.VERIFIED_ACCOUNT_ALREADY_EXIST,
                        paymentType = paymentType,
                        entryPoint = entryPoint
                    )
                } else {
                    binding.bankAccountView.setBankView(
                        bankAccount, BankAccountView.BankStatus.MANUAL_MATCHING_IN_PROGRESS,
                        paymentType = paymentType,
                        entryPoint = entryPoint
                    )
                }
            }
            else -> {
                binding.bankAccountView.setBankView(
                    bankAccount, BankAccountView.BankStatus.VERIFIED,
                    paymentType = paymentType,
                    entryPoint = entryPoint
                )
            }
        }
    }

    private fun showPaymentDownBottomSheet(isServiceDown: Boolean, message: String? = null) {
        val paymentDownBottomSheet = PaymentDownBottomSheet.createInstance(isServiceDown, message)
        paymentDownBottomSheet.show(supportFragmentManager, PaymentDownBottomSheet.TAG)
    }

    private fun handleErrorApi(message: String?, code: String, bankDetails: BankValidationRequest?) {
        with(binding) {
            when {
                code.equals(PpobConst.IN_ACTIVE, ignoreCase = true) -> {
                    AddUsedBankBottomSheet.newInstance(message)
                        .show(supportFragmentManager, "AddUsedBankBottomsheet")
                }
                code.equals(PpobConst.BLOCKED, ignoreCase = true) -> {
                    showSnackBar()
                    bankAccountView.setBankView(
                        BankAccount(
                            bankCode = bankDetails?.bankCode.orEmpty(),
                            accountNumber = bankDetails?.accountNumber
                        ),
                        BankAccountView.BankStatus.BLOCKED,
                        paymentType = paymentType,
                        entryPoint = entryPoint
                    )
                    txtLabelAccountNumber.setTextColor(getColorCompat(R.color.red_80))
                    inputAccountNumber.setTextColor(getColorCompat(R.color.red_80))
                    layoutInputAccountNumber.boxStrokeColor = getColorCompat(R.color.red_80)
                }
                code.equals(PpobConst.NOT_SUPPORTED, ignoreCase = true) -> {
                    bankAccountView.setBankView(
                        BankAccount(
                            bankCode = bankDetails?.bankCode.orEmpty(),
                            accountNumber = bankDetails?.accountNumber
                        ),
                        BankAccountView.BankStatus.UNSUPPORTED,
                        paymentType = paymentType,
                        entryPoint = entryPoint
                    )
                    txtLabelAccountNumber.setTextColor(getColorCompat(R.color.red_80))
                    inputAccountNumber.setTextColor(getColorCompat(R.color.red_80))
                    layoutInputAccountNumber.boxStrokeColor = getColorCompat(R.color.red_80)
                }
                else -> {
                    if (message == AppConst.NO_INTERNET_ERROR_MESSAGE) {
                        showPaymentDownBottomSheet(false)
                    } else {
                        binding.txtErrorAccount.text = if (!message.isNullOrBlank()) message
                        else getString(R.string.bank_account_not_found)
                    }
                }
            }
        }
    }

    private fun showProfileDialog() {
        val dialog = NullProfileReferralDialog(
                this,
                R.string.null_profile_payment_content,
                hideBtn = true
        ) {}
        dialog.show()
        scope.launch {
            delay(1000)
            if (isFinishing || isDestroyed) return@launch
            dialog.dismiss()
            val intent = BusinessProfileFormActivity.getIntent(this@AddBankAccountActivity)
            intent.putExtra(isRedirectFromPaymentsToProfile, true)
            startActivityForResult(intent, RC_START_BUSINESS_PROFILE)
        }
    }

    override fun onOnboardingDismiss(id: String?, body: String, isFromButton: Boolean, isFromCloseButton: Boolean, isFromOutside: Boolean) {
        // do nothing
    }

    override fun onOnboardingButtonClicked(id: String?, isFromHighlight: Boolean) {
        // do nothing
    }

    private fun showSnackBar() {
        val snackBar = Snackbar.make(binding.root, R.string.blocked_snack_bar_error, Snackbar.LENGTH_SHORT)
        snackBar.setTextColor(ContextCompat.getColor(this, R.color.red_80))
        val imgClose = ImageView(this)
        imgClose.scaleType = ImageView.ScaleType.CENTER_INSIDE
        val layImageParams = ViewGroup.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.MATCH_PARENT)
        imgClose.setImageResource(R.drawable.ic_close_button_red)
        val textViewAction = snackBar.view.findViewById(R.id.snackbar_action) as TextView
        (textViewAction.parent as SnackbarContentLayout).addView(imgClose, layImageParams)
        imgClose.setOnClickListener { snackBar.dismiss() }
        snackBar.setBackgroundTint(ContextCompat.getColor(this, R.color.red_5))
        val layoutParams = ActionBar.LayoutParams(snackBar.view.layoutParams)
        layoutParams.gravity = Gravity.TOP
        layoutParams.setMargins(0, 56.dp, 0, 0)
        snackBar.view.layoutParams = layoutParams
        snackBar.show()
    }
}
