package com.bukuwarung.bulk.di

import com.bukuwarung.bulk.data.local.CashCategoryDao
import com.bukuwarung.bulk.data.local.CashCategoryDataStore
import com.bukuwarung.bulk.data.local.CashCategoryStore
import com.bukuwarung.bulk.data.remote.CashCategoryRemoteDataStore
import com.bukuwarung.bulk.data.remote.CashCategoryRemoteStore
import com.bukuwarung.bulk.mapper.CashCategoryEntityMapper
import com.bukuwarung.bulk.mapper.CashCategoryEntityMapping
import com.bukuwarung.bulk.repository.CashCategoryRepo
import com.bukuwarung.bulk.repository.CashCategoryRepository
import com.bukuwarung.bulk.usecase.UserUniqueDetail
import com.bukuwarung.database.AppDatabase
import com.google.firebase.firestore.FirebaseFirestore
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
class CashCategoryDI {

    @Provides
    fun provideCashCategoryDao(appDatabase: AppDatabase): CashCategoryDao {
        return appDatabase.cashCategoryDao()
    }

    @Provides
    fun provideCashCategoryStore(cashCategoryDao: CashCategoryDao): CashCategoryStore {
        return CashCategoryDataStore(cashCategoryDao)
    }

    @Provides
    fun provideCashCategoryRemoteStore(fireStore: FirebaseFirestore): CashCategoryRemoteStore {
        return CashCategoryRemoteDataStore(fireStore)
    }

    @Provides
    @Singleton
    fun provideCashCategoryRepository(cashCategoryStore: CashCategoryStore,
                                      cashCategoryRemoteStore: CashCategoryRemoteStore): CashCategoryRepository {
        return CashCategoryRepo(cashCategoryStore, cashCategoryRemoteStore)
    }


    @Provides
    fun provideCashCategoryEntityMapper(userUniqueDetail: UserUniqueDetail): CashCategoryEntityMapper =
            CashCategoryEntityMapping(userUniqueDetail)


}