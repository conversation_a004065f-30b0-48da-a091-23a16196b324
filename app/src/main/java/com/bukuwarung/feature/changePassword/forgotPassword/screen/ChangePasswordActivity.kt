package com.bukuwarung.feature.login.createPassword.screen

import android.app.Activity
import android.content.Intent
import android.graphics.PorterDuff
import android.graphics.PorterDuffColorFilter
import android.os.Bundle
import android.text.Editable
import android.text.InputType
import android.text.TextWatcher
import android.view.View
import android.widget.TextView
import android.widget.Toast
import androidx.activity.viewModels
import androidx.core.content.ContextCompat
import com.bukuwarung.R
import com.bukuwarung.activities.home.MainActivity
import com.bukuwarung.activities.onboarding.LoginViewModel
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.base.data.api.Response
import com.bukuwarung.base.udf.api.screen.UdfActivity
import com.bukuwarung.base.udf.api.screen.viewBinding
import com.bukuwarung.base.udf.api.state.observeState
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst
import com.bukuwarung.databinding.ActivityChangePasswordBinding
import com.bukuwarung.neuro.api.Neuro
import com.bukuwarung.ui_component.component.button.BukuButton
import com.bukuwarung.ui_component.utils.hideView
import com.bukuwarung.ui_component.utils.showView
import dagger.hilt.android.AndroidEntryPoint
import java.util.regex.Matcher
import java.util.regex.Pattern
import javax.inject.Inject


@AndroidEntryPoint
class ChangePasswordActivity : UdfActivity() {

    private var isPasswordVisible: Boolean = false
    private var isConfirmPasswordVisible: Boolean = false
    private var isOldPasswordVisible: Boolean = false
    val PASSWORD_PATTERN = "^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[.,_!~*@#%&+])(?=\\S+$).{8,}$"
    val PASSWORD_PATTERN_MINIMUM_CHARACTER = "^(?=\\S+$).{8,}$"
    val PASSWORD_PATTERN_LOWER_CASE = ".*[a-z].*"
    val PASSWORD_PATTERN_UPPER_CASE = ".*[A-Z].*"
    val PASSWORD_PATTERN_NUMBER = ".*[0-9].*"
    val PASSWORD_PATTERN_SPECIAL_CHARACTER = ".*[.,_!~*@#%&+].*"
    val patternArraylist = ArrayList<String>()

    var SPECIAL_CHARACTERS_NOT_ALLOWED = " $'()/:;<=>?[]^`{|}"



    private var phone = ""
    private var countryCode = ""
    private var userId = ""

    @Inject
    lateinit var neuro: Neuro

    private val viewBinding: ActivityChangePasswordBinding by viewBinding()

    private val viewModel: ChangePasswordViewModel by viewModels()

    private val loginViewModel: LoginViewModel by viewModels()

    private fun setupView() {
        registerChangePasswordPageVisit()
        showScreen()
        viewBinding.btnEnter.disableButton()

        if (intent.hasExtra(VERIFY_OTP_PARAM_PHONE)) {
            val intent = intent
            phone = intent.getStringExtra(VERIFY_OTP_PARAM_PHONE) ?: ""
            countryCode = intent.getStringExtra(VERIFY_OTP_PARAM_COUNTRY_CODE) ?: ""

            //            this.otp = intent.getStringExtra("otp");
        }

        viewBinding.toolbar.setNavigationOnClickListener({
            // back button pressed
            onBackPressed()
        })

        viewBinding.btnEnter.setOnClickListener {

            if(validateNoOtherSpecialCharacter(viewBinding.tvPassword.text.toString())){
                viewBinding.tvPasswordError.showView()
                viewBinding.tvPasswordError.text = getString(R.string.other_special_characters_error)
                registerPasswordAlertAppear(AnalyticsConst.UNACCEPTED_SPECIAL_CHAR)
                return@setOnClickListener
            }else{
                viewBinding.tvPasswordError.hideView()
            }

            viewModel.changePassword(viewBinding.tvOldPassword.text.toString(),viewBinding.tvPassword.text.toString())
            showProgressState()
        }

        val textWatcher : TextWatcher = object : TextWatcher {

            override fun afterTextChanged(s: Editable) {}

            override fun beforeTextChanged(
                s: CharSequence, start: Int,
                count: Int, after: Int
            ) {
            }

            override fun onTextChanged(
                s: CharSequence, start: Int,
                before: Int, count: Int
            ) {
                if(isValidPassword(viewBinding.tvPassword.text.toString())){
                    viewBinding.btnEnter.setButtonType(BukuButton.ButtonType.YELLOW_60.string)
                    viewBinding.btnEnter.isEnabled = true
                }else {
                    viewBinding.btnEnter.disableButton()
                }

                if(isValidPassword(viewBinding.tvPassword.text.toString()) &&
                    viewBinding.tvPassword.text.toString().equals(viewBinding.tvConfirmPassword.text.toString())) {
                    viewBinding.tvPasswordMatch.showView()
                    registerPasswordAlertAppear(AnalyticsConst.PASSWORD_MATCH)
                }else{
                    viewBinding.tvPasswordMatch.hideView()
                }

                updatePasswordRequirement(viewBinding.tvPassword.text.toString())

            }
        }

        viewBinding.tvPassword.addTextChangedListener(textWatcher)
        viewBinding.tvPassword.onFocusChangeListener = View.OnFocusChangeListener { view, hasFocus -> if(hasFocus) registerPasswordFieldClick(AnalyticsConst.NEW_PASSWORD)}

        viewBinding.tvConfirmPassword.addTextChangedListener(textWatcher)

        viewBinding.tvConfirmPassword.onFocusChangeListener = View.OnFocusChangeListener { view, hasFocus -> if(hasFocus) registerPasswordFieldClick(AnalyticsConst.CONFIRM_PASSWORD)}
        viewBinding.tvOldPassword.onFocusChangeListener = View.OnFocusChangeListener { view, hasFocus -> if(hasFocus) registerPasswordFieldClick(AnalyticsConst.CURRENT_PASSWORD)}

        viewBinding.passwordLayout.setEndIconOnClickListener {
            isPasswordVisible = !isPasswordVisible
            if(isPasswordVisible) {
                viewBinding.tvPassword.inputType = InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD
            }else{
                viewBinding.tvPassword.inputType = AppConst.INPUT_TYPE_PASSWORD
            }
            registerPasswordVisiblityClick("new_password",isPasswordVisible)
        }

        viewBinding.oldPasswordLayout.setEndIconOnClickListener {
            isOldPasswordVisible = !isOldPasswordVisible
            if(isOldPasswordVisible) {
                viewBinding.tvOldPassword.inputType = InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD
            }else{
                viewBinding.tvOldPassword.inputType = AppConst.INPUT_TYPE_PASSWORD
            }
            registerPasswordVisiblityClick("current_password",isOldPasswordVisible)
        }

        viewBinding.userNameLayout.setEndIconOnClickListener {
            isConfirmPasswordVisible = !isConfirmPasswordVisible
            if(isConfirmPasswordVisible) {
                viewBinding.tvConfirmPassword.inputType = InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD
            }else{
                viewBinding.tvConfirmPassword.inputType = AppConst.INPUT_TYPE_PASSWORD
            }
            registerPasswordVisiblityClick("confirm_password",isConfirmPasswordVisible)
        }

        patternArraylist.add(PASSWORD_PATTERN_NUMBER)
        patternArraylist.add(PASSWORD_PATTERN_MINIMUM_CHARACTER)
        patternArraylist.add(PASSWORD_PATTERN_LOWER_CASE)
        patternArraylist.add(PASSWORD_PATTERN_UPPER_CASE)
        patternArraylist.add(PASSWORD_PATTERN_SPECIAL_CHARACTER)

    }

    override fun onBackPressed() {
        registerOnBackPressClick()
        super.onBackPressed()
    }

    private fun registerPasswordFieldClick(fieldType : String) {
        val propBuilder = AppAnalytics.PropBuilder()
        propBuilder.put(AnalyticsConst.FIELD, fieldType)
        propBuilder.put(AnalyticsConst.SCREEN_NAME, AnalyticsConst.CHANGE_PASSWORD_SCREEN)
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_SET_PASSWORD_BOX_CLICK, propBuilder)
    }

    private fun registerPasswordVisiblityClick(fieldType : String,action : Boolean) {
        val propBuilder = AppAnalytics.PropBuilder()
        propBuilder.put(AnalyticsConst.FIELD, fieldType)
        propBuilder.put(AnalyticsConst.ACTION, if (action) AnalyticsConst.SHOW else AnalyticsConst.HIDE)
        propBuilder.put(AnalyticsConst.SCREEN_NAME, AnalyticsConst.CHANGE_PASSWORD_SCREEN)
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_SET_PASSWORD_SHOW_CLICK, propBuilder)
    }

    private fun registerPasswordAlertAppear(alert : String) {
        val propBuilder = AppAnalytics.PropBuilder()
        propBuilder.put(AnalyticsConst.ALERT, alert)
        propBuilder.put(AnalyticsConst.SCREEN_NAME, AnalyticsConst.CHANGE_PASSWORD_SCREEN)
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_SET_PASSWORD_ALERT_APPEAR, propBuilder)
    }

    private fun registerOnBackPressClick() {
        val propBuilder = AppAnalytics.PropBuilder()
        propBuilder.put(AnalyticsConst.SCREEN_NAME, AnalyticsConst.CHANGE_PASSWORD_SCREEN)
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_BACK_BUTTON_PRESS, propBuilder)
    }

    private fun validateNoOtherSpecialCharacter(str: String): Boolean {
        for (i in 0 until SPECIAL_CHARACTERS_NOT_ALLOWED.length) {
            //Checking if the input string contain any of the specified Characters
            if (str.contains(Character.toString(SPECIAL_CHARACTERS_NOT_ALLOWED.elementAt(i)))) {
                return true
            }
        }
        return false
    }

    private fun registerChangePasswordPageVisit() {
        val propBuilder = AppAnalytics.PropBuilder()
        propBuilder.put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.LAINNYA_MENU)
        propBuilder.put(AnalyticsConst.SETTING_REASON, AnalyticsConst.CHANGE_PIN)
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_SET_PASSWORD_PAGE_VISIT, propBuilder)
    }

    private fun registerSubmitEvent(status : String ,failureReason: String) {
        val propBuilder = AppAnalytics.PropBuilder()
        propBuilder.put(AnalyticsConst.STATUS, status)
        propBuilder.put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.LAINNYA_MENU)
        propBuilder.put(AnalyticsConst.SETTING_REASON, AnalyticsConst.CHANGE)
        propBuilder.put(AnalyticsConst.FAIL_REASON, failureReason)
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_SET_PASSWORD_SUMIT, propBuilder)
    }


    private fun updatePasswordRequirement(password: String) {
        patternArraylist.forEach {
            val pattern: Pattern
            val matcher: Matcher
            pattern = Pattern.compile(it)
            matcher = pattern.matcher(password)

            if (it.equals(PASSWORD_PATTERN_NUMBER)) {
                if (matcher.matches()) {
                    enableCriteria(viewBinding.passwordCriteria4, R.color.green_60)
                } else {
                    disableCriteria(viewBinding.passwordCriteria4, R.color.black20)
                }
            }
            if (it.equals(PASSWORD_PATTERN_LOWER_CASE)) {
                if (matcher.matches()) {
                    enableCriteria(viewBinding.passwordCriteria2, R.color.green_60)
                } else {
                    disableCriteria(viewBinding.passwordCriteria2, R.color.black20)
                }
            }
            if (it.equals(PASSWORD_PATTERN_UPPER_CASE)) {
                if (matcher.matches()) {
                    enableCriteria(viewBinding.passwordCriteria3, R.color.green_60)
                } else {
                    disableCriteria(viewBinding.passwordCriteria3, R.color.black20)
                }
            }
            if (it.equals(PASSWORD_PATTERN_MINIMUM_CHARACTER)) {
                if (matcher.matches()) {
                    enableCriteria(viewBinding.passwordCriteria1, R.color.green_60)
                } else {
                    disableCriteria(viewBinding.passwordCriteria1, R.color.black20)
                }
            }
            if (it.equals(PASSWORD_PATTERN_SPECIAL_CHARACTER)) {
                if (matcher.matches()) {
                    enableCriteria(viewBinding.passwordCriteria5, R.color.green_60)
                } else {
                    disableCriteria(viewBinding.passwordCriteria5, R.color.black20)
                }
            }
        }
    }

    private fun disableCriteria(tv: TextView,color: Int) {
        enableCriteria(tv,color)
    }

    private fun enableCriteria(tv : TextView,color : Int) {
        tv.setTextColor(getColor(color))
        val drawables = tv.compoundDrawables
        drawables.forEach {
            it?.setColorFilter(
                PorterDuffColorFilter(
                    ContextCompat.getColor(
                        tv.getContext(),
                        color
                    ), PorterDuff.Mode.SRC_IN
                )
            )
        }
    }

    private fun isValidPassword(password: String) : Boolean {
        val pattern: Pattern
        val matcher: Matcher
        pattern = Pattern.compile(PASSWORD_PATTERN)
        matcher = pattern.matcher(password)
        return matcher.matches() && viewBinding.tvPassword.text.toString().equals(viewBinding.tvConfirmPassword.text.toString()) && viewBinding.tvOldPassword.text.toString().isNotEmpty()
    }

    private fun goToHome() {
        startActivity(Intent(this,MainActivity::class.java))
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(viewBinding.root)

        setupView()
        observeState(source = viewModel.stateFlow, action = ::render)
    }

    private fun render(state: ChangePasswordScreenState) {
        showScreen()
        handleNavigation(state)
        if(state.error!=null) handleError(state.error)
        handlePasswordCreation(state)
    }

    private fun handlePasswordCreation(state: ChangePasswordScreenState) {

        if(state.changePasswordResponse?.success==true){
            registerSubmitEvent(AnalyticsConst.STATUS_SUCCESS,AnalyticsConst.NONE)
            finish()
        }

    }


    private fun handleError(state: Response.Error?) {

        if(state?.meta?.get("statusCode")?.equals(455) == true) {
            viewBinding.tvError.showView()
            viewBinding.tvError.text = state.message
            registerSubmitEvent(AnalyticsConst.STATUS_FAIL,AnalyticsConst.WRONG_PASSWORD)
        }else if (state?.meta?.get("statusCode")?.equals(409)==true) {
            viewBinding.tvPasswordError.showView()
            viewBinding.tvPasswordError.text = state.message
            registerSubmitEvent(AnalyticsConst.STATUS_FAIL,AnalyticsConst.CONTAIN_OLD_PASSWORDS)
            registerPasswordAlertAppear(AnalyticsConst.CONTAIN_OLD_PASSWORDS)
        }
        else {
            if(state?.meta?.get("statusCode")?.equals(500)==true) {
                registerSubmitEvent(AnalyticsConst.STATUS_FAIL,AnalyticsConst.SERVER_ERROR)
            }
            Toast.makeText(this,state?.message,Toast.LENGTH_SHORT).show()
        }
    }


    private fun handleNavigation(state: ChangePasswordScreenState) {
        if (!state.isHandleNavigation) return
//        neuro.navigate(
//            context = this,
//            path = "/main",
//            query = mapOf(
//                "IS_NEW_LOGIN_EXTRA" to true,
//                "tab_name" to "HOME",
//            ),
//        )
        finish()
    }

    private fun showScreen() {
        viewBinding.progressbar.visibility = View.GONE
        viewBinding.successLayout.visibility = View.VISIBLE
        viewBinding.serverErrorLayout.visibility = View.GONE
    }

    private fun showErrorState() {
        with(viewBinding) {
            viewBinding.progressbar.hideView()
            viewBinding.serverErrorLayout.showView()
            viewBinding.successLayout.hideView()
        }
    }

    private fun showProgressState() {
        with(viewBinding) {
            viewBinding.progressbar.showView()
            viewBinding.serverErrorLayout.hideView()
            viewBinding.successLayout.hideView()
        }
    }


    companion object {
        private const val VERIFY_OTP_PARAM_PHONE = "phone"
        private const val VERIFY_OTP_PARAM_COUNTRY_CODE = "country"
        fun createIntent(origin: Activity?, phone: String?, countryCode: String): Intent {
            val intent = Intent(origin, ForgotPasswordActivity::class.java)
            intent.putExtra(VERIFY_OTP_PARAM_PHONE, phone)
            intent.putExtra(VERIFY_OTP_PARAM_COUNTRY_CODE, countryCode.replace("+", ""))
            return intent
        }
    }

}